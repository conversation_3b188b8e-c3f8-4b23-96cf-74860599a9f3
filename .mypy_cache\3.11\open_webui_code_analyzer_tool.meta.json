{"data_mtime": 1751373833, "dep_lines": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 2040, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "typing", "pydantic", "<PERSON><PERSON><PERSON>", "time", "pickle", "os", "threading", "json", "dataclasses", "collections", "re", "builtins", "_collections_abc", "_frozen_importlib", "_hashlib", "_io", "_pickle", "_thread", "_typeshed", "abc", "annotated_types", "enum", "http", "http.cookiejar", "io", "json.decoder", "json.encoder", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "requests.auth", "requests.exceptions", "requests.models", "types", "typing_extensions"], "hash": "90e79177995fd4eafed7a69093a53eab8a17f54e", "id": "open_webui_code_analyzer_tool", "ignore_all": false, "interface_hash": "1854367eac65529adf17df2e67e5707d370b5e45", "mtime": 1751374038, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\open_webui_code_analyzer_tool.py", "plugin_data": null, "size": 100601, "suppressed": [], "version_id": "1.15.0"}